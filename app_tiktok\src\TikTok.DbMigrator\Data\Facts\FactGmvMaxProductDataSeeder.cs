using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Data;
using TikTok.DbMigrator.Consts;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Entities;
using TikTok.Entities.Dim;
using TikTok.Enums;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace TikTok.DbMigrator.Data.Facts
{
    public class FactGmvMaxProductDataSeeder : ICustomDataSeedContributor
    {
        private readonly IRepository<FactGmvMaxProductEntity, Guid> _factRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;
        private readonly IRepository<DimProductEntity, Guid> _dimProductRepository;
        private readonly ILogger<FactGmvMaxProductDataSeeder> _logger;
        public int Order { get; set; } = 10;
        public FactGmvMaxProductDataSeeder(
            IRepository<FactGmvMaxProductEntity, Guid> factRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IRepository<DimStoreEntity, Guid> dimStoreRepository,
            IRepository<DimProductEntity, Guid> dimProductRepository,
            ILogger<FactGmvMaxProductDataSeeder> logger)
        {
            _factRepository = factRepository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _dimCampaignRepository = dimCampaignRepository;
            _dimStoreRepository = dimStoreRepository;
            _dimProductRepository = dimProductRepository;
            _logger = logger;
        }

        public async Task RunSeedAsync(DataSeedContext context)
        {
            if (await _factRepository.AnyAsync())
            {
                _logger.LogDebug("FactGmvMaxProduct data already exists, skipping seeding.");
                return;
            }

            _logger.LogDebug("Starting FactGmvMaxProduct data seeding...");

            var dimDates = await _dimDateRepository.GetListAsync();
            var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync();
            var dimAdAccounts = await _dimAdAccountRepository.GetListAsync();
            var dimCampaigns = await _dimCampaignRepository.GetListAsync();
            var dimStores = await _dimStoreRepository.GetListAsync();
            var dimProducts = await _dimProductRepository.GetListAsync();

            if (!dimDates.Any() || !dimBusinessCenters.Any() || !dimAdAccounts.Any() || !dimCampaigns.Any() || !dimStores.Any() || !dimProducts.Any())
            {
                _logger.LogWarning("Required dimension data not found. Please seed DimDate, DimBusinessCenter, DimAdAccount, DimCampaign, DimStore, and DimProduct first.");
                return;
            }

            var endDate = DateTime.Today;
            var startDate = endDate.AddDays(-30);
            var relevantDates = dimDates.Where(x => x.FullDate >= startDate && x.FullDate <= endDate).OrderBy(x => x.FullDate).ToList();

            var currentBusinessCenters = dimBusinessCenters.Where(x => x.IsCurrent).ToList();
            var currentAdAccounts = dimAdAccounts.Where(x => x.IsCurrent).ToList();
            var currentCampaigns = dimCampaigns.Where(x => x.IsCurrent).ToList();
            var currentStores = dimStores.ToList();
            var currentProducts = dimProducts.ToList();

            if (!currentBusinessCenters.Any() || !currentAdAccounts.Any() || !currentCampaigns.Any() || !currentStores.Any() || !currentProducts.Any())
            {
                _logger.LogWarning("Current dimension data not sufficient. Ensure IsCurrent records exist for BC, AdAccount, Campaign, and data exists for Store, Product.");
                return;
            }

            // Build quick lookup by business keys when possible
            var bcByBcId = currentBusinessCenters.GroupBy(b => b.BcId).ToDictionary(g => g.Key, g => g.First());
            var adByAdvertiserId = currentAdAccounts.GroupBy(a => a.AdvertiserId).ToDictionary(g => g.Key, g => g.First());
            var adByOwnerBcId = currentAdAccounts.Where(a => !string.IsNullOrWhiteSpace(a.OwnerBcId)).GroupBy(a => a.OwnerBcId).ToDictionary(g => g.Key, g => g.ToList());

            // Deterministic picker helper
            Func<int, int, int> pickIndex = (max, seed) => max == 0 ? 0 : Math.Abs(seed) % max;

            var toInsert = new List<FactGmvMaxProductEntity>();
            const int productsPerCampaignPerDay = 3;
            const int batchSize = 2000;

            foreach (var campaign in currentCampaigns)
            {
                // Try map campaign -> ad account via business key
                DimAdAccountEntity? adAccount = null;
                if (!string.IsNullOrWhiteSpace(campaign.AdvertiserId) && adByAdvertiserId.TryGetValue(campaign.AdvertiserId, out var mappedAd))
                {
                    adAccount = mappedAd;
                }
                else
                {
                    var idx = pickIndex(currentAdAccounts.Count, campaign.CampaignId.GetHashCode());
                    adAccount = currentAdAccounts[idx];
                }

                // Map ad account -> business center
                DimBusinessCenterEntity? businessCenter = null;
                if (!string.IsNullOrWhiteSpace(adAccount.OwnerBcId) && bcByBcId.TryGetValue(adAccount.OwnerBcId, out var mappedBc))
                {
                    businessCenter = mappedBc;
                }
                else
                {
                    var idx = pickIndex(currentBusinessCenters.Count, adAccount.AdvertiserId.GetHashCode());
                    businessCenter = currentBusinessCenters[idx];
                }

                if (adAccount == null || businessCenter == null)
                {
                    // Skip if mapping fails for any reason
                    continue;
                }

                // Currency from ad account
                var currency = (adAccount.Currency ?? "USD").ToUpperInvariant();

                foreach (var dimDate in relevantDates)
                {
                    var seedBase = HashCode.Combine(campaign.CampaignId, dimDate.Id);

                    // Deterministically pick a store set for this campaign/day
                    var storeIdx = pickIndex(currentStores.Count, seedBase);
                    var store = currentStores[storeIdx];

                    // Deterministically pick N products for this campaign/day
                    for (int i = 0; i < productsPerCampaignPerDay; i++)
                    {
                        var productIdx = pickIndex(currentProducts.Count, HashCode.Combine(seedBase, i));
                        var product = currentProducts[productIdx];

                        var record = CreateProductPerformance(
                            campaign,
                            adAccount,
                            businessCenter,
                            store,
                            product,
                            dimDate,
                            currency);

                        toInsert.Add(record);

                        if (toInsert.Count >= batchSize)
                        {
                            await _factRepository.InsertManyAsync(toInsert);
                            toInsert.Clear();
                        }
                    }
                }
            }

            if (toInsert.Count > 0)
            {
                await _factRepository.InsertManyAsync(toInsert);
                toInsert.Clear();
            }

            _logger.LogDebug("Successfully seeded FactGmvMaxProduct records.");
        }

        public Task SeedAsync(DataSeedContext context)
        {
            if (DbMigratorConst.IsEnableSeed)
            {
                return RunSeedAsync(context);
            }

            return Task.CompletedTask;
        }


        private FactGmvMaxProductEntity CreateProductPerformance(
            DimCampaignEntity campaign,
            DimAdAccountEntity adAccount,
            DimBusinessCenterEntity businessCenter,
            DimStoreEntity store,
            DimProductEntity product,
            DimDateEntity dimDate,
            string currency)
        {
            var seed = HashCode.Combine(campaign.CampaignId, store.StoreId, product.ProductId, dimDate.Id);
            var random = new Random(seed);

            // Product level metrics - more focused for new entity structure
            var orders = Math.Max(1, (int)(random.Next(1, 20) + random.NextDouble() * 5)); // 1-25 orders per product

            // Product level revenue
            decimal productPrice = product.CurrentPrice ?? (decimal)(50000 + random.NextDouble() * 950000); // 50k - 1M
            var grossRevenue = Math.Round(orders * productPrice * (decimal)(0.8 + random.NextDouble() * 0.4), 2); // 80-120% of price * orders

            // Calculate cost per order (simplified - no more campaign-level cost splitting)
            var costPerOrderBase = grossRevenue * (decimal)(0.1 + random.NextDouble() * 0.3) / orders; // 10-40% of revenue per order
            var costPerOrder = Math.Round(costPerOrderBase, 2);

            // Calculate total cost
            var totalCost = Math.Round(costPerOrder * orders, 2);

            // Calculate KPIs
            decimal? roas = totalCost > 0 ? Math.Round(grossRevenue / totalCost, 4) : (decimal?)null;
            decimal? tacos = grossRevenue > 0 ? Math.Round(totalCost / grossRevenue, 4) : (decimal?)null;

            // Currency conversion
            var (cpoVnd, cpoUsd) = ConvertCurrency(costPerOrder, currency);
            var (grossVnd, grossUsd) = ConvertCurrency(grossRevenue, currency);

            // Product specific fields
            var productImageUrl = $"https://shop.tiktok.com/product/{product.ProductId}/image_{random.Next(1, 10)}.jpg";
            var productStatus = random.Next(100) < 90 ? "available" : "unavailable"; // 90% available

            // Creative Delivery Status - weighted distribution
            var creativeDeliveryStatus = GenerateCreativeDeliveryStatus(random);

            // Creative fields (only for some products)
            string? creativeType = null;
            string? ttAccountName = null;
            string? ttAccountProfileImageUrl = null;
            string? ttAccountAuthorizationType = null;
            string? shopContentType = null;
            
            if (random.Next(100) < 60) // 60% have creative data
            {
                var creativeTypes = new[] { "ADS_AND_ORGANIC", "ORGANIC", "REMOVED" };
                creativeType = creativeTypes[random.Next(creativeTypes.Length)];
                
                if (creativeType != "REMOVED")
                {
                    ttAccountName = $"TikTok Creator {random.Next(1000, 9999)}";
                    ttAccountProfileImageUrl = $"https://tiktok.com/profile/{random.Next(100000, 999999)}.jpg";
                    
                    var authTypes = new[] { "TTS_TT", "AFFILIATE", "TT_USER", "BC_AUTH_TT", "AUTH_CODE", "UNSET" };
                    ttAccountAuthorizationType = authTypes[random.Next(authTypes.Length)];
                    
                    var contentTypes = new[] { "VIDEO", "PRODUCT_CARD" };
                    shopContentType = contentTypes[random.Next(contentTypes.Length)];
                }
            }

            // Product engagement metrics (if has creative)
            long? productImpressions = null;
            long? productClicks = null;
            decimal? productClickRate = null;
            decimal? adClickRate = null;
            decimal? adConversionRate = null;
            
            if (creativeType != null && creativeType != "REMOVED")
            {
                productImpressions = (long)(random.Next(1000, 50000) + random.NextDouble() * 5000);
                var clickRate = 0.01m + (decimal)random.NextDouble() * 0.05m; // 1% - 6%
                productClicks = Math.Max(1, (long)Math.Round(productImpressions.Value * (double)clickRate));
                productClickRate = Math.Round(clickRate, 4);
                
                adClickRate = Math.Round(0.005m + (decimal)random.NextDouble() * 0.03m, 4); // 0.5% - 3.5%
                adConversionRate = Math.Round(0.01m + (decimal)random.NextDouble() * 0.1m, 4); // 1% - 11%
            }

            // Video view rates (if video content)
            decimal? adVideoViewRate2s = null;
            decimal? adVideoViewRate6s = null;
            decimal? adVideoViewRateP25 = null;
            decimal? adVideoViewRateP50 = null;
            decimal? adVideoViewRateP75 = null;
            decimal? adVideoViewRateP100 = null;
            
            if (shopContentType == "VIDEO")
            {
                adVideoViewRate2s = Math.Round(0.7m + (decimal)random.NextDouble() * 0.25m, 4); // 70% - 95%
                adVideoViewRate6s = Math.Round(0.5m + (decimal)random.NextDouble() * 0.3m, 4); // 50% - 80%
                adVideoViewRateP25 = Math.Round(0.4m + (decimal)random.NextDouble() * 0.3m, 4); // 40% - 70%
                adVideoViewRateP50 = Math.Round(0.25m + (decimal)random.NextDouble() * 0.25m, 4); // 25% - 50%
                adVideoViewRateP75 = Math.Round(0.15m + (decimal)random.NextDouble() * 0.2m, 4); // 15% - 35%
                adVideoViewRateP100 = Math.Round(0.05m + (decimal)random.NextDouble() * 0.15m, 4); // 5% - 20%
            }

            return new FactGmvMaxProductEntity
            {
                DimDateId = dimDate.Id,
                DimBusinessCenterId = businessCenter.Id,
                DimAdAccountId = adAccount.Id,
                DimCampaignId = campaign.Id,
                DimStoreId = store.Id,
                DimProductId = product.Id,
                CampaignId = campaign.CampaignId,
                StoreId = store.StoreId,
                ProductId = product.ProductId,
                BcId = businessCenter.BcId,
                AdvertiserId = adAccount.AdvertiserId,
                ProductName = product.ProductName,
                ProductImageUrl = productImageUrl,
                ProductStatus = productStatus,
                
                // Creative fields
                CreativeType = creativeType,
                TtAccountName = ttAccountName,
                TtAccountProfileImageUrl = ttAccountProfileImageUrl,
                TtAccountAuthorizationType = ttAccountAuthorizationType,
                ShopContentType = shopContentType,
                
                // Core metrics
                Orders = orders,
                CostPerOrder = costPerOrder,
                CostPerOrderVND = Math.Round(cpoVnd, 2),
                CostPerOrderUSD = Math.Round(cpoUsd, 2),
                GrossRevenue = Math.Round(grossRevenue, 2),
                GrossRevenueVND = Math.Round(grossVnd, 2),
                GrossRevenueUSD = Math.Round(grossUsd, 2),
                
                // Product engagement metrics
                ProductImpressions = productImpressions,
                ProductClicks = productClicks,
                ProductClickRate = productClickRate,
                AdClickRate = adClickRate,
                AdConversionRate = adConversionRate,
                
                // Video metrics
                AdVideoViewRate2s = adVideoViewRate2s,
                AdVideoViewRate6s = adVideoViewRate6s,
                AdVideoViewRateP25 = adVideoViewRateP25,
                AdVideoViewRateP50 = adVideoViewRateP50,
                AdVideoViewRateP75 = adVideoViewRateP75,
                AdVideoViewRateP100 = adVideoViewRateP100,
                
                // Performance metrics
                ROAS = roas,
                TACOS = tacos,
                
                Currency = currency,
                Date = dimDate.FullDate,
                Cost = totalCost,
                CreativeDeliveryStatus = creativeDeliveryStatus
            };
        }

        private CreativeDeliveryStatus GenerateCreativeDeliveryStatus(Random random)
        {
            // Weighted distribution for realistic creative delivery status
            var rand = random.Next(100);

            if (rand < 40) return CreativeDeliveryStatus.DELIVERING; // 40% delivering (most common)
            if (rand < 55) return CreativeDeliveryStatus.LEARNING; // 15% learning
            if (rand < 65) return CreativeDeliveryStatus.IN_QUEUE; // 10% in queue
            if (rand < 75) return CreativeDeliveryStatus.NOT_DELIVERYIN; // 10% not delivering
            if (rand < 85) return CreativeDeliveryStatus.AUTHORIZATION_NEEDED; // 10% auth needed
            if (rand < 92) return CreativeDeliveryStatus.EXCLUDED; // 7% excluded
            if (rand < 97) return CreativeDeliveryStatus.UNAVAILABLE; // 5% unavailable
            return CreativeDeliveryStatus.REJECTED; // 3% rejected
        }

        private (decimal vndAmount, decimal usdAmount) ConvertCurrency(decimal amount, string sourceCurrency)
        {
            var exchangeRates = new Dictionary<string, (decimal vndRate, decimal usdRate)>
            {
                { "VND", (1.0m, 0.000041m) },
                { "USD", (24350.0m, 1.0m) },
                { "EUR", (26500.0m, 1.09m) },
                { "GBP", (31000.0m, 1.27m) },
                { "JPY", (165.0m, 0.0068m) },
                { "KRW", (18.5m, 0.00076m) },
                { "CNY", (3400.0m, 0.14m) },
                { "SGD", (18000.0m, 0.74m) },
                { "THB", (680.0m, 0.028m) },
                { "MYR", (5200.0m, 0.21m) }
            };

            var key = (sourceCurrency ?? "USD").ToUpperInvariant();
            if (exchangeRates.TryGetValue(key, out var rates))
            {
                var vndAmount = amount * rates.vndRate;
                var usdAmount = amount * rates.usdRate;
                return (vndAmount, usdAmount);
            }

            return (amount * 24350.0m, amount);
        }
    }
}


