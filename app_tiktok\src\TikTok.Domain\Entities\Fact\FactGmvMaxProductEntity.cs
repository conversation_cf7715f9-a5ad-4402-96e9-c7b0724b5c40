using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TikTok.Entities;
using TikTok.Entities.Dim;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Domain.Entities.DataWarehouse.Fact;

/// <summary>
/// Fact table cho GMV Max Product Detail - Chi tiết hiệu suất từng sản phẩm
/// Phục vụ drill-down analysis theo sản phẩm
/// Được xây dựng từ dữ liệu thô: RawGmvMaxProductDetailProductReportEntity, RawGmvMaxProductCreativeReportEntity
/// </summary>
[Table("Fact_GmvMaxProduct")]
public class FactGmvMaxProductEntity : AuditedEntity<Guid>
{
    /// <summary>
    /// Foreign Key đến Dim_Date
    /// </summary>
    [Required]
    public int DimDateId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_BusinessCenter
    /// </summary>
    [Required]
    public Guid DimBusinessCenterId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_AdAccount
    /// </summary>
    [Required]
    public Guid DimAdAccountId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_Campaign
    /// </summary>
    [Required]
    public Guid DimCampaignId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_Store (TikTok Shop)
    /// </summary>
    [Required]
    public Guid DimStoreId { get; set; }

    /// <summary>
    /// Foreign Key đến Dim_Product
    /// </summary>
    [Required]
    public Guid DimProductId { get; set; }

    /// <summary>
    /// Business Key - Campaign ID từ TikTok
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.CampaignId, RawGmvMaxProductCreativeReportEntity.CampaignId
    /// </summary>
    [Required]
    [StringLength(100)]
    public string CampaignId { get; set; }

    /// <summary>
    /// Business Key - Store ID từ TikTok Shop
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.StoreId, RawGmvMaxProductCreativeReportEntity.StoreId
    /// </summary>
    [Required]
    [StringLength(100)]
    public string StoreId { get; set; }

    /// <summary>
    /// Business Key - Product ID từ TikTok Shop
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.ItemGroupId, RawGmvMaxProductCreativeReportEntity.ItemGroupId
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ProductId { get; set; }

    /// <summary>
    /// Business Key - Business Center ID từ TikTok
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.BcId, RawGmvMaxProductCreativeReportEntity.BcId
    /// </summary>
    [Required]
    [StringLength(100)]
    public string BcId { get; set; }

    /// <summary>
    /// Business Key - Advertiser ID từ TikTok
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.AdvertiserId, RawGmvMaxProductCreativeReportEntity.AdvertiserId
    /// </summary>
    [Required]
    [StringLength(100)]
    public string AdvertiserId { get; set; }

    /// <summary>
    /// Tên sản phẩm
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.ProductName, RawGmvMaxProductCreativeReportEntity.Title
    /// </summary>
    [StringLength(500)]
    public string? ProductName { get; set; }

    /// <summary>
    /// URL hình ảnh sản phẩm
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.ProductImageUrl
    /// </summary>
    [StringLength(1000)]
    public string? ProductImageUrl { get; set; }

    /// <summary>
    /// Trạng thái sản phẩm (available, unavailable)
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.ProductStatus
    /// </summary>
    [StringLength(20)]
    public string? ProductStatus { get; set; }

    /// <summary>
    /// Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.CreativeType
    /// </summary>
    [StringLength(20)]
    public string? CreativeType { get; set; }

    /// <summary>
    /// Tên TikTok account
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.TtAccountName
    /// </summary>
    [StringLength(200)]
    public string? TtAccountName { get; set; }

    /// <summary>
    /// URL hình đại diện TikTok account
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.TtAccountProfileImageUrl
    /// </summary>
    [StringLength(1000)]
    public string? TtAccountProfileImageUrl { get; set; }

    /// <summary>
    /// Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.TtAccountAuthorizationType
    /// </summary>
    [StringLength(20)]
    public string? TtAccountAuthorizationType { get; set; }

    /// <summary>
    /// Loại nội dung shop (VIDEO, PRODUCT_CARD)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ShopContentType
    /// </summary>
    [StringLength(20)]
    public string? ShopContentType { get; set; }

    /// <summary>
    /// Số lượng đơn hàng (Orders)
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.Orders, RawGmvMaxProductCreativeReportEntity.Orders
    /// </summary>
    public int Orders { get; set; }

    /// <summary>
    /// Chi phí trung bình mỗi đơn hàng (CPO)
    /// Nguồn: RawGmvMaxProductCampaignReportEntity.CostPerOrder, RawGmvMaxProductCreativeReportEntity.CostPerOrder
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostPerOrder { get; set; }

    /// <summary>
    /// Chi phí trung bình mỗi đơn hàng (CPO) (VND)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostPerOrderVND { get; set; }

    /// <summary>
    /// Chi phí trung bình mỗi đơn hàng (CPO) (USD)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? CostPerOrderUSD { get; set; }

    /// <summary>
    /// Tổng doanh thu (Gross Revenue)
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.GrossRevenue, RawGmvMaxProductCreativeReportEntity.GrossRevenue
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal GrossRevenue { get; set; }

    /// <summary>
    /// Tổng doanh thu (Gross Revenue) (VND)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? GrossRevenueVND { get; set; }

    /// <summary>
    /// Tổng doanh thu (Gross Revenue) (USD)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? GrossRevenueUSD { get; set; }

    /// <summary>
    /// Tổng lượt hiển thị sản phẩm (organic + paid)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ProductImpressions
    /// </summary>
    public long? ProductImpressions { get; set; }

    /// <summary>
    /// Tổng lượt click sản phẩm (organic + paid)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ProductClicks
    /// </summary>
    public long? ProductClicks { get; set; }

    /// <summary>
    /// Tỷ lệ click sản phẩm (ProductClicks/ProductImpressions)
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.ProductClickRate
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? ProductClickRate { get; set; }

    /// <summary>
    /// Tỷ lệ click-through của paid views từ video này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdClickRate
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdClickRate { get; set; }

    /// <summary>
    /// Tỷ lệ chuyển đổi của paid clicks từ video này
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdConversionRate
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdConversionRate { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 2 giây
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRate2s
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRate2s { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 6 giây
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRate6s
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRate6s { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 25% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP25
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP25 { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 50% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP50
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP50 { get; set; }

    /// <summary>
    /// Tỷ lệ xem video ít nhất 75% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP75
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP75 { get; set; }

    /// <summary>
    /// Tỷ lệ xem video 100% thời lượng
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.AdVideoViewRateP100
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? AdVideoViewRateP100 { get; set; }

    /// <summary>
    /// ROAS (Return on Ad Spend) - Hiệu quả quảng cáo
    /// Nguồn: RawGmvMaxProductCampaignReportEntity.ROI (mapped to ROAS)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? ROAS { get; set; }

    /// <summary>
    /// TACOS (True ACOS) - Hiệu quả quảng cáo so với tổng doanh thu
    /// Công thức: Cost / GrossRevenue (Cost được phân bổ từ campaign level)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? TACOS { get; set; }

    /// <summary>
    /// Tiền tệ
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.Currency, RawGmvMaxProductCreativeReportEntity.Currency
    /// </summary>
    [Required]
    [StringLength(10)]
    public string Currency { get; set; }

    /// <summary>
    /// Ngày báo cáo UTC format yyyy-MM-dd 00:00:00 (theo ngày)
    /// Nguồn: RawGmvMaxProductDetailProductReportEntity.Date, RawGmvMaxProductCreativeReportEntity.Date (converted to date only)
    /// </summary>
    [Required]
    public DateTime Date { get; set; }
    /// <summary>
    /// Tổng chi phí
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.Cost
    /// </summary>
    [Column(TypeName = "decimal(15,2)")]
    public decimal Cost { get; set; }
    /// <summary>
    /// Trạng thái creative
    /// Nguồn: RawGmvMaxProductCreativeReportEntity.CreativeDeliveryStatus
    /// </summary>
    public CreativeDeliveryStatus CreativeDeliveryStatus { get; set; }
    /// <summary>
    /// Navigation Properties
    /// </summary>
    public virtual DimDateEntity DimDate { get; set; }
    public virtual DimBusinessCenterEntity DimBusinessCenter { get; set; }
    public virtual DimAdAccountEntity DimAdAccount { get; set; }
    public virtual DimCampaignEntity DimCampaign { get; set; }
    public virtual DimStoreEntity DimStore { get; set; }
    public virtual DimProductEntity DimProduct { get; set; }
}
